import { formatNormalDate } from '../../utils/util';
import reviewApi from '../../../api/modules/review';

Page({
  data: {
    reviewDetail: {}, // 评价详情
    orderInfo: {}, // 订单信息
    orderId: '', // 订单ID
  },

  onLoad(options) {
    // 从本地存储获取订单信息
    const orderInfo = wx.getStorageSync('orderInfo');
    if (orderInfo) {
      this.setOrderInfo(orderInfo);
    }

    // 从本地存储获取评价信息
    const reviewInfo = wx.getStorageSync('reviewInfo');
    if (reviewInfo) {
      this.setReviewDetail(reviewInfo);
    }
  },

  // 设置订单信息
  setOrderInfo(orderInfo) {
    console.log('orderInfo1: ', orderInfo);
    // 处理敏感信息，只显示必要的信息
    const processedOrderInfo = {
      productName: orderInfo.productName,
      productImage: orderInfo.productImage,
      extraServive: orderInfo.extraServive || [],
      petName: orderInfo.petName,
      expectTime: orderInfo.expectTime,
      // 客户姓名脱敏处理
      customerName: this.maskCustomerName(orderInfo.customer?.nickname || orderInfo.customer?.name || '客户'),
      // 地址脱敏处理
      serviceAddress: this.maskAddress(orderInfo.addressDetail || orderInfo.userAdress || ''),
      // 用户备注
      userRemark: orderInfo.userRemark || '',
    };

    this.setData({
      orderId: orderInfo.id,
      orderInfo: processedOrderInfo,
    });
  },

  // 客户姓名脱敏
  maskCustomerName(name) {
    if (!name || name.length <= 1) return name;
    if (name.length === 2) {
      return name.charAt(0) + '*';
    }
    return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1);
  },

  // 地址脱敏
  maskAddress(address) {
    if (!address) return '';
    // 只显示前面的区域信息，隐藏具体门牌号
    const parts = address.split(/[()（）]/);
    if (parts.length > 1) {
      // 如果有括号，只显示括号前的部分
      return parts[0] + '***';
    }
    // 如果地址较长，只显示前半部分
    if (address.length > 10) {
      return address.substring(0, Math.floor(address.length / 2)) + '***';
    }
    return address;
  },

  // 设置评价详情数据
  setReviewDetail(reviewInfo) {
    // 处理评价图片数据
    let images = [];
    if (reviewInfo.images) {
      if (typeof reviewInfo.images === 'string') {
        // 如果是字符串，尝试解析JSON
        try {
          images = JSON.parse(reviewInfo.images);
        } catch (e) {
          // 如果解析失败，按逗号分割
          images = reviewInfo.images.split(',').filter(img => img.trim());
        }
      } else if (Array.isArray(reviewInfo.images)) {
        images = reviewInfo.images;
      }
    }

    this.setData({
      reviewDetail: {
        ...reviewInfo,
        // 格式化时间
        createdAt: reviewInfo.createdAt ? formatNormalDate(reviewInfo.createdAt) : '',
        updatedAt: reviewInfo.updatedAt ? formatNormalDate(reviewInfo.updatedAt) : '',
        // 格式化评分显示
        ratingStars: this.generateStars(reviewInfo.rating || 0),
        // 处理图片数据
        images: images,
      },
    });
  },

  // 生成星级评分显示
  generateStars(rating) {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    // 添加实心星星
    for (let i = 0; i < fullStars; i++) {
      stars.push({ type: 'full' });
    }

    // 添加半星
    if (hasHalfStar) {
      stars.push({ type: 'half' });
    }

    // 添加空心星星
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push({ type: 'empty' });
    }

    return stars;
  },

  // 预览图片
  previewImage(e) {
    const { url, urls } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: urls || [url],
    });
  },
});
